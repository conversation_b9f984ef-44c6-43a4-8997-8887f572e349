// Portfolio data - Replace with your actual project data
const portfolioData = [
    {
        id: 1,
        title: "E-commerce Website",
        category: "websites",
        image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop",
        description: "Modern e-commerce platform with custom WordPress theme",
        technologies: ["WordPress", "WooCommerce", "Custom CSS", "JavaScript"],
        link: "#"
    },
    {
        id: 2,
        title: "Brand Identity Design",
        category: "graphics",
        image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=500&h=300&fit=crop",
        description: "Complete brand identity package including logo and guidelines",
        technologies: ["Adobe Illustrator", "Photoshop", "Brand Strategy"],
        link: "#"
    },
    {
        id: 3,
        title: "Mobile App Prototype",
        category: "ux-ui",
        image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
        description: "Interactive prototype for fitness tracking mobile application",
        technologies: ["Figma", "Prototyping", "User Research", "Wireframing"],
        link: "#"
    },
    {
        id: 4,
        title: "Social Media Campaign",
        category: "marketing",
        image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop",
        description: "Comprehensive social media marketing campaign for tech startup",
        technologies: ["Facebook Ads", "Instagram", "Analytics", "Content Strategy"],
        link: "#"
    },
    {
        id: 5,
        title: "Restaurant Website",
        category: "websites",
        image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=500&h=300&fit=crop",
        description: "Responsive restaurant website with online ordering system",
        technologies: ["WordPress", "Custom Theme", "Online Ordering", "SEO"],
        link: "#"
    },
    {
        id: 6,
        title: "Product Packaging Design",
        category: "graphics",
        image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=500&h=300&fit=crop",
        description: "Creative packaging design for organic food products",
        technologies: ["Adobe Illustrator", "Print Design", "Brand Guidelines"],
        link: "#"
    },
    {
        id: 7,
        title: "Dashboard UI Design",
        category: "ux-ui",
        image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop",
        description: "Clean and intuitive dashboard interface for analytics platform",
        technologies: ["Figma", "UI Design", "Data Visualization", "Prototyping"],
        link: "#"
    },
    {
        id: 8,
        title: "PPC Campaign Management",
        category: "marketing",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
        description: "Google Ads campaign optimization for B2B software company",
        technologies: ["Google Ads", "Analytics", "Conversion Tracking", "A/B Testing"],
        link: "#"
    }
];

// DOM Elements
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const navbar = document.getElementById('navbar');
const portfolioGrid = document.getElementById('portfolio-grid');
const filterBtns = document.querySelectorAll('.filter-btn');

// Mobile Navigation Toggle
navToggle.addEventListener('click', () => {
    navMenu.classList.toggle('active');
    navToggle.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
    });
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Active navigation link highlighting
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Portfolio filtering
function renderPortfolio(items) {
    portfolioGrid.innerHTML = '';
    
    items.forEach(item => {
        const portfolioItem = document.createElement('div');
        portfolioItem.className = 'portfolio-item';
        portfolioItem.innerHTML = `
            <div class="portfolio-card">
                <div class="portfolio-image">
                    <img src="${item.image}" alt="${item.title}" loading="lazy">
                    <div class="portfolio-overlay">
                        <div class="portfolio-content">
                            <h3>${item.title}</h3>
                            <p>${item.description}</p>
                            <div class="portfolio-tech">
                                ${item.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                            </div>
                            <a href="${item.link}" class="portfolio-link">
                                <i class="fas fa-external-link-alt"></i>
                                View Project
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
        portfolioGrid.appendChild(portfolioItem);
    });
}

// Filter functionality
filterBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        // Remove active class from all buttons
        filterBtns.forEach(b => b.classList.remove('active'));
        // Add active class to clicked button
        btn.classList.add('active');
        
        const filter = btn.getAttribute('data-filter');
        
        if (filter === 'all') {
            renderPortfolio(portfolioData);
        } else {
            const filteredItems = portfolioData.filter(item => item.category === filter);
            renderPortfolio(filteredItems);
        }
    });
});

// Initialize portfolio on page load
document.addEventListener('DOMContentLoaded', () => {
    renderPortfolio(portfolioData);
    
    // Add scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.service-card, .portfolio-item, .stat-item').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Contact form handling (if you add a contact form)
function handleContactForm() {
    const form = document.getElementById('contact-form');
    if (form) {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            // Add your form submission logic here
            alert('Thank you for your message! I\'ll get back to you soon.');
            form.reset();
        });
    }
}

// Call contact form handler
handleContactForm();

// Typing animation for hero title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing animation
window.addEventListener('load', () => {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const originalText = heroTitle.innerHTML;
        typeWriter(heroTitle, originalText, 50);
    }
});

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const heroCards = document.querySelectorAll('.hero-card');
    
    heroCards.forEach((card, index) => {
        const speed = 0.5 + (index * 0.1);
        card.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// Add loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
});

// Performance optimization: Lazy loading for images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}
