<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapple Agency - Portfolio</title>
    <link rel="stylesheet" href="portfolio-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Portfolio-only specific styles */
        body {
            margin: 0;
            padding: 0;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
        }
        
        .portfolio-only {
            min-height: 100vh;
            padding: 4rem 0;
        }
        
        .portfolio-header {
            text-align: center;
            margin-bottom: 4rem;
        }
        
        .portfolio-header h1 {
            font-family: 'Playfair Display', serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .portfolio-header p {
            font-size: 1.2rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }
        
        .mapple-logo {
            display: inline-block;
            margin-bottom: 2rem;
        }
        
        .mapple-logo h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
        }
        
        .mapple-logo span {
            color: var(--secondary-color);
        }
        
        @media (max-width: 768px) {
            .portfolio-header h1 {
                font-size: 2.5rem;
            }
            
            .portfolio-only {
                padding: 2rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Portfolio Section Only -->
    <section class="portfolio portfolio-only">
        <div class="container">
            <div class="portfolio-header">
                <div class="mapple-logo">
                    <h2>Mapple<span>Agency</span></h2>
                </div>
                <h1>Nuestro Portfolio</h1>
                <p>Una muestra de nuestro trabajo reciente y proyectos creativos que demuestran nuestra experiencia en diseño digital y marketing</p>
            </div>
            
            <div class="portfolio-filters">
                <button class="filter-btn active" data-filter="all">Todos los Proyectos</button>
                <button class="filter-btn" data-filter="websites">Sitios Web</button>
                <button class="filter-btn" data-filter="graphics">Diseño Gráfico</button>
                <button class="filter-btn" data-filter="ux-ui">UX/UI</button>
                <button class="filter-btn" data-filter="marketing">Marketing</button>
            </div>
            
            <div class="portfolio-grid" id="portfolio-grid">
                <!-- Portfolio items will be dynamically generated by JavaScript -->
            </div>
        </div>
    </section>

    <script src="portfolio-script.js"></script>
</body>
</html>
