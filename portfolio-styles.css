/* Mapple Agency Portfolio - CSS Variables */
:root {
    --primary-color: #00bcd4;
    --secondary-color: #e91e63;
    --accent-color: #ff6b9d;
    --text-primary: #ffffff;
    --text-secondary: #b0bec5;
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-dark: #000000;
    --border-color: #333333;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Portfolio Section */
.portfolio {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Portfolio Filters */
.portfolio-filters {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background: transparent;
    color: var(--text-secondary);
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: transparent;
    color: white;
    transform: translateY(-2px);
}

/* Portfolio Grid */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.portfolio-item {
    background: var(--bg-secondary);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    opacity: 1;
    transform: scale(1);
}

.portfolio-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.portfolio-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 188, 212, 0.9), rgba(233, 30, 99, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.portfolio-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.portfolio-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    line-height: 1.5;
}

.portfolio-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    padding: 0.25rem 0.75rem;
    background: var(--bg-primary);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .portfolio {
        padding: 4rem 0;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .portfolio-filters {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 2rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    .portfolio-content {
        padding: 1rem;
    }
}

/* Animation for portfolio items */
.portfolio-item {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation for portfolio items */
.portfolio-item:nth-child(1) { animation-delay: 0.1s; }
.portfolio-item:nth-child(2) { animation-delay: 0.2s; }
.portfolio-item:nth-child(3) { animation-delay: 0.3s; }
.portfolio-item:nth-child(4) { animation-delay: 0.4s; }
.portfolio-item:nth-child(5) { animation-delay: 0.5s; }
.portfolio-item:nth-child(6) { animation-delay: 0.6s; }
.portfolio-item:nth-child(7) { animation-delay: 0.7s; }
.portfolio-item:nth-child(8) { animation-delay: 0.8s; }
