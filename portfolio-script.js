// Mapple Agency Portfolio Data
const portfolioData = [
    {
        id: 1,
        title: "Sitio Web E-commerce",
        category: "websites",
        image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop",
        description: "Plataforma e-commerce moderna con tema WordPress personalizado",
        technologies: ["WordPress", "WooCommerce", "CSS Personalizado", "JavaScript"],
        link: "#"
    },
    {
        id: 2,
        title: "Identidad de Marca Completa",
        category: "graphics",
        image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=500&h=300&fit=crop",
        description: "Paquete completo de identidad de marca incluyendo logo y guías",
        technologies: ["Adobe Illustrator", "Photoshop", "Estrategia de Marca"],
        link: "#"
    },
    {
        id: 3,
        title: "Prototipo de App Móvil",
        category: "ux-ui",
        image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=300&fit=crop",
        description: "Prototipo interactivo para aplicación móvil de fitness",
        technologies: ["Figma", "Prototipado", "Investigación de Usuario", "Wireframing"],
        link: "#"
    },
    {
        id: 4,
        title: "Campaña de Redes Sociales",
        category: "marketing",
        image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop",
        description: "Campaña integral de marketing en redes sociales para startup tech",
        technologies: ["Facebook Ads", "Instagram", "Analytics", "Estrategia de Contenido"],
        link: "#"
    },
    {
        id: 5,
        title: "Sitio Web Restaurante",
        category: "websites",
        image: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=500&h=300&fit=crop",
        description: "Sitio web responsivo para restaurante con sistema de pedidos online",
        technologies: ["WordPress", "Tema Personalizado", "Pedidos Online", "SEO"],
        link: "#"
    },
    {
        id: 6,
        title: "Diseño de Empaque de Producto",
        category: "graphics",
        image: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=500&h=300&fit=crop",
        description: "Diseño creativo de empaque para productos alimenticios orgánicos",
        technologies: ["Adobe Illustrator", "Diseño de Impresión", "Guías de Marca"],
        link: "#"
    },
    {
        id: 7,
        title: "Diseño UI Dashboard",
        category: "ux-ui",
        image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=300&fit=crop",
        description: "Interfaz limpia e intuitiva para plataforma de analytics",
        technologies: ["Figma", "Diseño UI", "Visualización de Datos", "Prototipado"],
        link: "#"
    },
    {
        id: 8,
        title: "Gestión de Campaña PPC",
        category: "marketing",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop",
        description: "Optimización de campaña Google Ads para empresa de software B2B",
        technologies: ["Google Ads", "Analytics", "Seguimiento de Conversiones", "A/B Testing"],
        link: "#"
    }
];

// Portfolio functionality
function renderPortfolio(items = portfolioData) {
    const portfolioGrid = document.getElementById('portfolio-grid');
    
    portfolioGrid.innerHTML = items.map(item => `
        <div class="portfolio-item" data-category="${item.category}">
            <div class="portfolio-image">
                <img src="${item.image}" alt="${item.title}" loading="lazy">
                <div class="portfolio-overlay">
                    <a href="${item.link}" class="portfolio-link">
                        <i class="fas fa-external-link-alt"></i>
                        Ver Proyecto
                    </a>
                </div>
            </div>
            <div class="portfolio-content">
                <h3>${item.title}</h3>
                <p>${item.description}</p>
                <div class="portfolio-tech">
                    ${item.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                </div>
            </div>
        </div>
    `).join('');
}

// Filter functionality
function setupFilters() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons
            filterBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            btn.classList.add('active');
            
            const filter = btn.getAttribute('data-filter');
            const portfolioItems = document.querySelectorAll('.portfolio-item');
            
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.classList.remove('hidden');
                } else {
                    item.classList.add('hidden');
                }
            });
        });
    });
}

// Initialize portfolio when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    renderPortfolio();
    setupFilters();
    
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
    
    // Add loading animation
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    portfolioItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
    });
});
